!pip3 install langgraph

from langgraph import Graph, Node
from langgraph.llms import OpenAI
from typing import List, Dict
import asyncio

# -----------------------------
# Todo Node Definition
# -----------------------------
todos: List[Dict] = []

class TodoNode(Node):
    """
    LangGraph node to manage todos with lifecycle:
    pending -> in_progress -> completed
    """
    
    def create_todo(self, content: str) -> Dict:
        todo = {"content": content, "status": "pending", "activeForm": ""}
        todos.append(todo)
        return todo
    
    def update_todo(self, index: int, status: str, active_form: str = "") -> Dict:
        if 0 <= index < len(todos):
            todos[index]["status"] = status
            todos[index]["activeForm"] = active_form
            return todos[index]
        return {}
    
    def list_todos(self, status_filter: str = None) -> List[Dict]:
        if status_filter:
            return [t for t in todos if t["status"] == status_filter]
        return todos
    
    def display_progress(self):
        if not todos:
            print("No todos yet.")
            return
        
        completed = len([t for t in todos if t["status"] == "completed"])
        in_progress = len([t for t in todos if t["status"] == "in_progress"])
        total = len(todos)
        
        print(f"\nProgress: {completed}/{total} completed")
        print(f"Currently working on: {in_progress} task(s)\n")
        
        for i, t in enumerate(todos):
            icon = "✅" if t["status"] == "completed" else \
                   "🔧" if t["status"] == "in_progress" else "❌"
            text = t["activeForm"] if t["status"] == "in_progress" else t["content"]
            print(f"{i + 1}. {icon} {text}")

# -----------------------------
# LangGraph Setup
# -----------------------------
llm = OpenAI(model="gpt-4", temperature=0)
graph = Graph(llm=llm)
todo_node = TodoNode(name="TodoManager")
graph.add_node(todo_node)

# -----------------------------
# Automated Todo Planning & Execution
# -----------------------------
async def agent_todo_workflow(prompt: str):
    """
    LLM generates tasks from a prompt, then automatically updates
    todos in progress and completion.
    """
    async for message in graph.query(prompt):
        # Assume message contains 'tool_use' events for todo planning
        if message.get("type") == "tool_use" and message.get("name") == "TodoWrite":
            # Add new todos suggested by the agent
            for todo_content in message["input"]["todos"]:
                todo_node.create_todo(todo_content)
            todo_node.display_progress()
        
        # Simulate agent working on first pending task
        pending = todo_node.list_todos("pending")
        if pending:
            index = todos.index(pending[0])
            todo_node.update_todo(index, "in_progress", active_form=f"Working on: {pending[0]['content']}")
            todo_node.display_progress()
            
            # Simulate completion
            await asyncio.sleep(1)  # mimic work time
            todo_node.update_todo(index, "completed")
            todo_node.display_progress()

# -----------------------------
# Run the agent workflow
# -----------------------------
asyncio.run(agent_todo_workflow("Build a complete authentication system with todos"))


