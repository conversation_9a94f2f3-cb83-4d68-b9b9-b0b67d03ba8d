{"cells": [{"cell_type": "code", "execution_count": null, "id": "c56e5b88", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 14, "id": "5c724a85", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pypdf in /opt/miniconda3/lib/python3.13/site-packages (6.1.0)\n"]}], "source": ["!pip install pypdf"]}, {"cell_type": "code", "execution_count": 16, "id": "e490cdeb", "metadata": {}, "outputs": [], "source": ["import json\n", "from pypdf import PdfReader, PdfWriter"]}, {"cell_type": "code", "execution_count": 20, "id": "f213cd55", "metadata": {}, "outputs": [], "source": ["PDF_PATH = \"forms/job-app-form-EDITED.pdf\"          # Input form (must be an AcroForm PDF)\n", "OUTPUT_PDF_PATH = \"filled.pdf\" # Output after filling\n", "MAPPING_JSON = \"field_mapping.json\"  # Store normalized names here"]}, {"cell_type": "code", "execution_count": 21, "id": "e20a1795", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'FULL NAME': {'/T': 'FULL NAME', '/FT': '/Tx', '/V': ''},\n", " 'DATE': {'/T': 'DATE', '/FT': '/Tx', '/V': ''},\n", " 'ADDRESS': {'/T': 'ADDRESS', '/FT': '/Tx', '/V': ''},\n", " 'City': {'/T': 'City', '/FT': '/Tx', '/V': ''},\n", " 'State': {'/T': 'State', '/FT': '/Tx', '/V': ''},\n", " 'Zip Code': {'/T': 'Zip Code', '/FT': '/Tx', '/V': ''},\n", " 'EMAIL': {'/T': 'EMAIL', '/FT': '/Tx', '/V': ''},\n", " 'PHONE': {'/T': 'PHONE', '/FT': '/Tx', '/V': ''},\n", " 'SSN First 3': {'/T': 'SSN First 3', '/FT': '/Tx', '/V': ''},\n", " 'SSN Second 3': {'/T': 'SSN Second 3', '/FT': '/Tx', '/V': ''},\n", " 'SSN Last 3': {'/T': 'SSN Last 3', '/FT': '/Tx', '/V': ''},\n", " 'DATE AVAILABLE': {'/T': 'DATE AVAILABLE', '/FT': '/Tx', '/V': ''},\n", " 'DESIRED PAY': {'/T': 'DESIRED PAY', '/FT': '/Tx', '/V': ''},\n", " 'HOUR': {'/T': 'HOUR', '/FT': '/Btn', '/_States_': ['/HOUR', '/Off']},\n", " 'SALARY': {'/T': 'SALARY', '/FT': '/Btn', '/_States_': ['/SALARY', '/Off']},\n", " 'POSITION APPLIED FOR': {'/T': 'POSITION APPLIED FOR',\n", "  '/FT': '/Tx',\n", "  '/V': ''},\n", " 'FULLTIME': {'/T': 'FULLTIME',\n", "  '/FT': '/Btn',\n", "  '/_States_': ['/FULLTIME', '/Off']},\n", " 'PARTTIME': {'/T': 'PARTTIME',\n", "  '/FT': '/Btn',\n", "  '/_States_': ['/PARTTIME', '/Off']},\n", " 'SEASONAL': {'/T': 'SEASONAL',\n", "  '/FT': '/Btn',\n", "  '/_States_': ['/SEASONAL', '/Off']},\n", " 'IF YES WRITE THE START AND END DATES': {'/T': 'IF YES WRITE THE START AND END DATES',\n", "  '/FT': '/Tx',\n", "  '/V': ''},\n", " 'IF YES PLEASE EXPLAIN': {'/T': 'IF YES PLEASE EXPLAIN',\n", "  '/FT': '/Tx',\n", "  '/V': ''},\n", " 'HIGH SCHOOL': {'/T': 'HIGH SCHOOL', '/FT': '/Tx', '/V': ''},\n", " 'CITY STATE HIGH SCHOOL': {'/T': 'CITY STATE HIGH SCHOOL',\n", "  '/FT': '/Tx',\n", "  '/V': ''},\n", " 'HIGH SCHOOL FROM': {'/T': 'HIGH SCHOOL FROM', '/FT': '/Tx', '/V': ''},\n", " 'HIGH SCHOOL TO': {'/T': 'HIGH SCHOOL TO', '/FT': '/Tx', '/V': ''},\n", " 'DIPLOMA TYPE': {'/T': 'DIPLOMA TYPE', '/FT': '/Tx', '/V': ''},\n", " 'YES GRADUATED': {'/T': 'YES GRADUATED',\n", "  '/FT': '/Btn',\n", "  '/_States_': ['/YES GRADUATED', '/Off']},\n", " 'NO GRADUATED': {'/T': 'NO GRADUATED',\n", "  '/FT': '/Btn',\n", "  '/_States_': ['/NO GRADUATED', '/Off']},\n", " 'COLLEGE': {'/T': 'COLLEGE', '/FT': '/Tx', '/V': ''},\n", " 'CITY STATE COLLEGE': {'/T': 'CITY STATE COLLEGE', '/FT': '/Tx', '/V': ''},\n", " 'COLLEGE FROM': {'/T': 'COLLEGE FROM', '/FT': '/Tx', '/V': ''},\n", " 'COLLEGE TO': {'/T': 'COLLEGE TO', '/FT': '/Tx', '/V': ''},\n", " 'COLLEGE DEGREE': {'/T': 'COLLEGE DEGREE', '/FT': '/Tx', '/V': ''},\n", " 'YES GRADUATED COLLEGE': {'/T': 'YES GRADUATED COLLEGE',\n", "  '/FT': '/Btn',\n", "  '/_States_': ['/YES GRADUATED COLLEGE', '/Off']},\n", " 'NO GRADUATED COLLEGE': {'/T': 'NO GRADUATED COLLEGE',\n", "  '/FT': '/Btn',\n", "  '/_States_': ['/NO GRADUATED COLLEGE', '/Off']},\n", " 'OTHER EDUCATION': {'/T': 'OTHER EDUCATION', '/FT': '/Tx', '/V': ''},\n", " 'CITY STATE OTHER EDUCATION': {'/T': 'CITY STATE OTHER EDUCATION',\n", "  '/FT': '/Tx',\n", "  '/V': ''},\n", " 'YES LEGALLY ELIGIBLE TO WORK IN US': {'/T': 'YES LEGALLY ELIGIBLE TO WORK IN US',\n", "  '/FT': '/Btn',\n", "  '/_States_': ['/YES LEGALLY ELIGIBLE TO WORK IN US', '/Off']},\n", " 'NO LEGALLY ELIGIBLE TO WORK IN US': {'/T': 'NO LEGALLY ELIGIBLE TO WORK IN US',\n", "  '/FT': '/Btn',\n", "  '/_States_': ['/NO LEGALLY ELIGIBLE TO WORK IN US', '/Off']}}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["reader = PdfReader(PDF_PATH)\n", "fields = reader.get_fields()\n", "fields"]}, {"cell_type": "code", "execution_count": null, "id": "8332215a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e895d287", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 22, "id": "d48bf64f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'/FT': '/Tx', '/T': 'FULL NAME', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [142.8, 655.08, 376.56, 676.56], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(153, 0, 4481192480), '/V': '', '/AP': IndirectObject(154, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'DATE', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [416.64, 655.08, 536.76, 676.56], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(165, 0, 4481192480), '/V': '', '/AP': IndirectObject(166, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'ADDRESS', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [132.84, 622.92, 540, 644.4], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(171, 0, 4481192480), '/V': '', '/AP': IndirectObject(172, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'City', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [131.76, 590.64, 259.44, 612.12], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(177, 0, 4481192480), '/V': '', '/AP': IndirectObject(178, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'State', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [263.88, 590.64, 397.68, 612.12], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(183, 0, 4481192480), '/V': '', '/AP': IndirectObject(184, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'Zip Code', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [402.12003, 590.64, 539.28, 612.12], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(189, 0, 4481192480), '/V': '', '/AP': IndirectObject(190, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'EMAIL', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [117, 558.36, 344.04004, 579.84], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(195, 0, 4481192480), '/V': '', '/AP': IndirectObject(196, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'PHONE', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [393.36, 558.36, 533.52, 579.84], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(201, 0, 4481192480), '/V': '', '/AP': IndirectObject(202, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'SSN First 3', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [263.88, 532.08, 297.24, 553.56], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(207, 0, 4481192480), '/V': '', '/AP': IndirectObject(208, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'SSN Second 3', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [301.32, 532.08, 327.6, 553.56], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(213, 0, 4481192480), '/V': '', '/AP': IndirectObject(214, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'SSN Last 3', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [331.68, 532.08, 365.04, 553.56], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(219, 0, 4481192480), '/V': '', '/AP': IndirectObject(220, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'DATE AVAILABLE', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [175.08, 508.32, 295.2, 529.8], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(225, 0, 4481192480), '/V': '', '/AP': IndirectObject(226, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'DESIRED PAY', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [385.08002, 508.32, 440.52002, 529.8], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(231, 0, 4481192480), '/V': '', '/AP': IndirectObject(232, 0, 4481192480)}\n", "{'/FT': '/Btn', '/T': 'HOUR', '/Type': '/Annot', '/Subtype': '/Widget', '/C': [0.0, 0.0, 0.0], '/Rect': [443.88, 509.16, 453.36, 515.4], '/F': 4, '/MK': IndirectObject(237, 0, 4481192480), '/DA': '/ZaDb 0 Tf 0.0 0.0 0.0 rg', '/P': IndirectObject(4, 0, 4481192480), '/AP': IndirectObject(238, 0, 4481192480), '/AS': '/Off'}\n", "{'/FT': '/Btn', '/T': 'SALARY', '/Type': '/Annot', '/Subtype': '/Widget', '/C': [0.0, 0.0, 0.0], '/Rect': [484.44003, 509.16, 493.92004, 515.4], '/F': 4, '/MK': IndirectObject(250, 0, 4481192480), '/DA': '/ZaDb 0 Tf 0.0 0.0 0.0 rg', '/P': IndirectObject(4, 0, 4481192480), '/AP': IndirectObject(251, 0, 4481192480), '/AS': '/Off'}\n", "{'/FT': '/Tx', '/T': 'POSITION APPLIED FOR', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [208.32, 485.04, 535.8, 506.52], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(263, 0, 4481192480), '/V': '', '/AP': IndirectObject(264, 0, 4481192480)}\n", "{'/FT': '/Btn', '/T': 'FULLTIME', '/Type': '/Annot', '/Subtype': '/Widget', '/C': [0.0, 0.0, 0.0], '/Rect': [211.80002, 462.35995, 221.28001, 468.59998], '/F': 4, '/MK': IndirectObject(269, 0, 4481192480), '/DA': '/ZaDb 0 Tf 0.0 0.0 0.0 rg', '/P': IndirectObject(4, 0, 4481192480), '/AP': IndirectObject(270, 0, 4481192480), '/AS': '/Off'}\n", "{'/FT': '/Btn', '/T': 'PARTTIME', '/Type': '/Annot', '/Subtype': '/Widget', '/C': [0.0, 0.0, 0.0], '/Rect': [270.6, 462.35995, 280.08, 468.59998], '/F': 4, '/MK': IndirectObject(282, 0, 4481192480), '/DA': '/ZaDb 0 Tf 0.0 0.0 0.0 rg', '/P': IndirectObject(4, 0, 4481192480), '/AP': IndirectObject(283, 0, 4481192480), '/AS': '/Off'}\n", "{'/FT': '/Btn', '/T': 'SEASONAL', '/Type': '/Annot', '/Subtype': '/Widget', '/C': [0.0, 0.0, 0.0], '/Rect': [330.36, 462.35995, 339.84, 468.59998], '/F': 4, '/MK': IndirectObject(295, 0, 4481192480), '/DA': '/ZaDb 0 Tf 0.0 0.0 0.0 rg', '/P': IndirectObject(4, 0, 4481192480), '/AP': IndirectObject(296, 0, 4481192480), '/AS': '/Off'}\n", "{'/FT': '/Tx', '/T': 'IF YES WRITE THE START AND END DATES', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [317.88, 336.48, 537.72, 357.48], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(308, 0, 4481192480), '/V': '', '/AP': IndirectObject(309, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'IF YES PLEASE EXPLAIN', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [218.40001, 290.76, 536.4001, 311.64], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(314, 0, 4481192480), '/V': '', '/AP': IndirectObject(315, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'HIGH SCHOOL', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [156.36002, 210.71996, 296.64, 232.19995], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(320, 0, 4481192480), '/V': '', '/AP': IndirectObject(321, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'CITY STATE HIGH SCHOOL', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [375.36, 210.71996, 516, 232.19995], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(326, 0, 4481192480), '/V': '', '/AP': IndirectObject(327, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'HIGH SCHOOL FROM', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [110.520004, 187.68002, 250.92001, 209.16003], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(332, 0, 4481192480), '/V': '', '/AP': IndirectObject(333, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'HIGH SCHOOL TO', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [275.64, 187.68002, 415.68, 209.16003], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(338, 0, 4481192480), '/V': '', '/AP': IndirectObject(339, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'DIPLOMA TYPE', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [258.36, 163.91997, 399, 185.39996], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(344, 0, 4481192480), '/V': '', '/AP': IndirectObject(345, 0, 4481192480)}\n", "{'/FT': '/Btn', '/T': 'YES GRADUATED', '/Type': '/Annot', '/Subtype': '/Widget', '/C': [0.0, 0.0, 0.0], '/Rect': [142.32, 164.76, 151.8, 171], '/F': 4, '/MK': IndirectObject(350, 0, 4481192480), '/DA': '/ZaDb 0 Tf 0.0 0.0 0.0 rg', '/P': IndirectObject(4, 0, 4481192480), '/AP': IndirectObject(351, 0, 4481192480), '/AS': '/Off'}\n", "{'/FT': '/Btn', '/T': 'NO GRADUATED', '/Type': '/Annot', '/Subtype': '/Widget', '/C': [0.0, 0.0, 0.0], '/Rect': [175.08, 164.76, 184.56, 171], '/F': 4, '/MK': IndirectObject(363, 0, 4481192480), '/DA': '/ZaDb 0 Tf 0.0 0.0 0.0 rg', '/P': IndirectObject(4, 0, 4481192480), '/AP': IndirectObject(364, 0, 4481192480), '/AS': '/Off'}\n", "{'/FT': '/Tx', '/T': 'COLLEGE', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [131.76, 140.75998, 272.16, 162.23999], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(376, 0, 4481192480), '/V': '', '/AP': IndirectObject(377, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'CITY STATE COLLEGE', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [351.12, 140.75998, 491.76, 162.23999], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(382, 0, 4481192480), '/V': '', '/AP': IndirectObject(383, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'COLLEGE FROM', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [110.520004, 117.719955, 250.92001, 139.19995], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(388, 0, 4481192480), '/V': '', '/AP': IndirectObject(389, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'COLLEGE TO', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [275.64, 117.719955, 415.68, 139.19995], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(394, 0, 4481192480), '/V': '', '/AP': IndirectObject(395, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'COLLEGE DEGREE', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [255.12, 94.19999, 395.76, 115.67999], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(400, 0, 4481192480), '/V': '', '/AP': IndirectObject(401, 0, 4481192480)}\n", "{'/FT': '/Btn', '/T': 'YES GRADUATED COLLEGE', '/Type': '/Annot', '/Subtype': '/Widget', '/C': [0.0, 0.0, 0.0], '/Rect': [142.32, 95.03997, 151.8, 101.27997], '/F': 4, '/MK': IndirectObject(406, 0, 4481192480), '/DA': '/ZaDb 0 Tf 0.0 0.0 0.0 rg', '/P': IndirectObject(4, 0, 4481192480), '/AP': IndirectObject(407, 0, 4481192480), '/AS': '/Off'}\n", "{'/FT': '/Btn', '/T': 'NO GRADUATED COLLEGE', '/Type': '/Annot', '/Subtype': '/Widget', '/C': [0.0, 0.0, 0.0], '/Rect': [175.08, 95.03997, 184.56, 101.27997], '/F': 4, '/MK': IndirectObject(419, 0, 4481192480), '/DA': '/ZaDb 0 Tf 0.0 0.0 0.0 rg', '/P': IndirectObject(4, 0, 4481192480), '/AP': IndirectObject(420, 0, 4481192480), '/AS': '/Off'}\n", "{'/FT': '/Tx', '/T': 'OTHER EDUCATION', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [117.600006, 70.91996, 257.4, 92.39996], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(432, 0, 4481192480), '/V': '', '/AP': IndirectObject(433, 0, 4481192480)}\n", "{'/FT': '/Tx', '/T': 'CITY STATE OTHER EDUCATION', '/DA': '/F3 12.0 Tf 0.0 0.0 0.0 rg', '/F': 4, '/Type': '/Annot', '/Subtype': '/Widget', '/Rect': [336.36, 70.91996, 476.76, 92.39996], '/P': IndirectObject(4, 0, 4481192480), '/MK': IndirectObject(438, 0, 4481192480), '/V': '', '/AP': IndirectObject(439, 0, 4481192480)}\n", "{'/FT': '/Btn', '/T': 'YES LEGALLY ELIGIBLE TO WORK IN US', '/Type': '/Annot', '/Subtype': '/Widget', '/C': [0.0, 0.0, 0.0], '/Rect': [356.3775, 381.8025, 368.3775, 393.8025], '/F': 4, '/MK': IndirectObject(444, 0, 4481192480), '/DA': '/ZaDb 0 Tf 0.0 0.0 0.0 rg', '/P': IndirectObject(4, 0, 4481192480), '/AP': IndirectObject(445, 0, 4481192480), '/AS': '/Off'}\n", "{'/FT': '/Btn', '/T': 'NO LEGALLY ELIGIBLE TO WORK IN US', '/Type': '/Annot', '/Subtype': '/Widget', '/C': [0.0, 0.0, 0.0], '/Rect': [387, 381.8025, 399, 393.8025], '/F': 4, '/MK': IndirectObject(457, 0, 4481192480), '/DA': '/ZaDb 0 Tf 0.0 0.0 0.0 rg', '/P': IndirectObject(4, 0, 4481192480), '/AP': IndirectObject(458, 0, 4481192480), '/AS': '/Off'}\n", "Extracted Fields: {'FULL NAME': '', 'DATE': '', 'ADDRESS': '', 'City': '', 'State': '', 'Zip Code': '', 'EMAIL': '', 'PHONE': '', 'SSN First 3': '', 'SSN Second 3': '', 'SSN Last 3': '', 'DATE AVAILABLE': '', 'DESIRED PAY': '', 'HOUR': '', 'SALARY': '', 'POSITION APPLIED FOR': '', 'FULLTIME': '', 'PARTTIME': '', 'SEASONAL': '', 'IF YES WRITE THE START AND END DATES': '', 'IF YES PLEASE EXPLAIN': '', 'HIGH SCHOOL': '', 'CITY STATE HIGH SCHOOL': '', 'HIGH SCHOOL FROM': '', 'HIGH SCHOOL TO': '', 'DIPLOMA TYPE': '', 'YES GRADUATED': '', 'NO GRADUATED': '', 'COLLEGE': '', 'CITY STATE COLLEGE': '', 'COLLEGE FROM': '', 'COLLEGE TO': '', 'COLLEGE DEGREE': '', 'YES GRADUATED COLLEGE': '', 'NO GRADUATED COLLEGE': '', 'OTHER EDUCATION': '', 'CITY STATE OTHER EDUCATION': '', 'YES LEGALLY ELIGIBLE TO WORK IN US': '', 'NO LEGALLY ELIGIBLE TO WORK IN US': ''}\n"]}], "source": ["def get_form_fields(pdf_path):\n", "    reader = PdfReader(pdf_path)\n", "    fields = {}\n", "    if \"/AcroForm\" in reader.trailer[\"/Root\"]:\n", "        form = reader.trailer[\"/Root\"][\"/AcroForm\"]\n", "        form_fields = form.get(\"/Fields\", [])\n", "        for field in form_fields:\n", "            field_obj = field.get_object()\n", "            print(field_obj)\n", "            name = field_obj.get(\"/T\")\n", "            if name:\n", "                fields[name] = \"\"\n", "    return fields\n", "\n", "fields = get_form_fields(PDF_PATH)\n", "print(\"Extracted Fields:\", fields)"]}, {"cell_type": "code", "execution_count": 23, "id": "4ff93054", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Field Mapping (raw→normalized):\n", "{\n", "  \"FULL NAME\": \"Normalized_0\",\n", "  \"DATE\": \"Normalized_1\",\n", "  \"ADDRESS\": \"Normalized_2\",\n", "  \"City\": \"Normalized_3\",\n", "  \"State\": \"Normalized_4\",\n", "  \"Zip Code\": \"Normalized_5\",\n", "  \"EMAIL\": \"Normalized_6\",\n", "  \"PHONE\": \"Normalized_7\",\n", "  \"SOCIAL SECURITY NUMBER SSN\": \"Normalized_8\",\n", "  \"undefined\": \"Normalized_9\",\n", "  \"undefined_2\": \"Normalized_10\",\n", "  \"DATE AVAILABLE\": \"Normalized_11\",\n", "  \"DESIRED PAY\": \"Normalized_12\",\n", "  \"HOUR\": \"Normalized_13\",\n", "  \"SALARY\": \"Normalized_14\",\n", "  \"POSITION APPLIED FOR\": \"Normalized_15\",\n", "  \"FULLTIME\": \"Normalized_16\",\n", "  \"PARTTIME\": \"Normalized_17\",\n", "  \"SEASONAL\": \"Normalized_18\",\n", "  \"ARE YOU LEGALLY ELIGIBLE TO WORK IN THE US\": \"Normalized_19\",\n", "  \"HAVE YOU EVER WORKED FOR THIS EMPLOYER\": \"Normalized_20\",\n", "  \"IF YES WRITE THE START AND END DATES\": \"Normalized_21\",\n", "  \"HAVE YOU EVER BEEN CONVICTED OF A FELONY\": \"Normalized_22\",\n", "  \"IF YES PLEASE EXPLAIN\": \"Normalized_23\",\n", "  \"HIGH SCHOOL\": \"Normalized_24\",\n", "  \"CITY  STATE\": \"Normalized_25\",\n", "  \"FROM\": \"Normalized_26\",\n", "  \"TO\": \"Normalized_27\",\n", "  \"YES_4\": \"Normalized_28\",\n", "  \"undefined_3\": \"Normalized_29\",\n", "  \"NO DIPLOMA\": \"Normalized_30\",\n", "  \"COLLEGE\": \"Normalized_31\",\n", "  \"CITY  STATE_2\": \"Normalized_32\",\n", "  \"FROM_2\": \"Normalized_33\",\n", "  \"TO_2\": \"Normalized_34\",\n", "  \"YES_5\": \"Normalized_35\",\n", "  \"undefined_4\": \"Normalized_36\",\n", "  \"NO DEGREE\": \"Normalized_37\",\n", "  \"OTHER\": \"Normalized_38\",\n", "  \"CITY  STATE_3\": \"Normalized_39\"\n", "}\n"]}], "source": ["# Step 3: Create or update mapping (normalize junk field names → human-readable)\n", "\n", "# If no mapping exists, create a stub mapping\n", "try:\n", "    with open(MAPPING_JSON, \"r\") as f:\n", "        field_mapping = json.load(f)\n", "except FileNotFoundError:\n", "    field_mapping = {k: f\"Normalized_{i}\" for i, k in enumerate(fields.keys())}\n", "    with open(MAPPING_JSON, \"w\") as f:\n", "        json.dump(field_mapping, f, indent=2)\n", "\n", "print(\"Field Mapping (raw→normalized):\")\n", "print(json.dumps(field_mapping, indent=2))\n"]}, {"cell_type": "code", "execution_count": 10, "id": "12b74bf3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Filled PDF saved to filled.pdf\n"]}], "source": ["# Step 4: Example data to fill (based on normalized names)\n", "# Imagine this comes from an LLM output\n", "normalized_data = {\n", "    \"FULL NAME\": \"Alice\",\n", "    \"ADDRESS\": \"<PERSON>\",\n", "    \"Has Insurance\": \"Yes\"\n", "}\n", "\n", "# Invert mapping: normalized → raw\n", "inv_mapping = {v: k for k, v in field_mapping.items()}\n", "\n", "# Step 5: Fill form fields\n", "def fill_pdf(input_pdf, output_pdf, inv_mapping, normalized_data):\n", "    reader = PdfReader(input_pdf)\n", "    writer = PdfWriter()\n", "\n", "    for page in reader.pages:\n", "        writer.add_page(page)\n", "\n", "    # Prepare field values using raw names\n", "    field_values = {}\n", "    for norm_name, value in normalized_data.items():\n", "        raw_name = inv_mapping.get(norm_name)\n", "        if raw_name:\n", "            field_values[raw_name] = value\n", "\n", "    writer.update_page_form_field_values(writer.pages[0], field_values)\n", "\n", "    with open(output_pdf, \"wb\") as f:\n", "        writer.write(f)\n", "\n", "fill_pdf(PDF_PATH, OUTPUT_PDF_PATH, inv_mapping, normalized_data)\n", "print(f\"✅ Filled PDF saved to {OUTPUT_PDF_PATH}\")\n"]}, {"cell_type": "code", "execution_count": 25, "id": "78d1cdf1", "metadata": {}, "outputs": [], "source": ["from pypdf import PdfReader, PdfWriter\n", "\n", "reader = PdfReader(\"forms/job-app-form-EDITED.pdf\")\n", "writer = PdfWriter()\n", "\n", "page = reader.pages[0]\n", "fields = reader.get_fields()\n", "\n", "writer.append(reader)\n", "\n", "# writer.update_page_form_field_values(\n", "#     writer.pages[0],\n", "#     {\"fieldname\": \"some filled in text\"},\n", "#     auto_regenerate=False,\n", "# )\n", "\n", "# with open(\"filled-out.pdf\", \"wb\") as output_stream:\n", "#     writer.write(output_stream)"]}, {"cell_type": "code", "execution_count": 27, "id": "375b7d25", "metadata": {}, "outputs": [], "source": ["fields\n", "\n", "writer.update_page_form_field_values(\n", "    writer.pages[0],\n", "    {\"FULL NAME\": \"some filled in text\"},\n", "    auto_regenerate=False,\n", ")\n", "\n", "with open(\"filled-out.pdf\", \"wb\") as output_stream:\n", "    writer.write(output_stream)"]}, {"cell_type": "code", "execution_count": null, "id": "225195d5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}